using UnityEngine;
using UnityEngine.UI;

public class LowHealthEffect : MonoBehaviour
{
    [Header("Effect Settings")]
    [Tooltip("Ngưỡng máu để bắt đầu hiệu ứng (giá trị 0-1). Nên để giống với lowHealthThreshold trong HealthBarUI")]
    [Range(0, 1)]
    [SerializeField] private float healthThreshold = 0.3f;

    [Tooltip("Cường độ hiệu ứng tối đa")]
    [Range(0, 1)]
    [SerializeField] private float maxEffectIntensity = 0.8f;

    [Tooltip("Tốc độ nhấp nháy (0 = không nhấp nháy)")]
    [Range(0, 5)]
    [SerializeField] private float pulseSpeed = 1.5f;

    [Tooltip("Cường độ nhấp nháy")]
    [Range(0, 1)]
    [SerializeField] private float pulseIntensity = 0.2f;

    // References
    private HealthSystem healthSystem;
    private Image overlayImage;
    private float baseAlpha;
    private float currentIntensity;

    private void Start()
    {
        // Tìm health system
        healthSystem = FindObjectOfType<HealthSystem>();
        if (healthSystem == null)
        {
            Debug.LogError("LowHealthEffect: Không tìm thấy HealthSystem!");
            return;
        }
        
        // Tìm HealthBarUI để đồng bộ ngưỡng màu đỏ
        HealthBarUI healthBarUI = FindObjectOfType<HealthBarUI>();
        if (healthBarUI != null)
        {
            // Thử lấy giá trị từ field bằng reflection
            try {
                System.Reflection.FieldInfo field = typeof(HealthBarUI).GetField("lowHealthThreshold", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (field != null) {
                    float lowThreshold = (float)field.GetValue(healthBarUI);
                    healthThreshold = lowThreshold;
                    Debug.Log("LowHealthEffect: Đã đồng bộ ngưỡng với HealthBarUI: " + lowThreshold);
                }
            } catch (System.Exception e) {
                Debug.LogWarning("LowHealthEffect: Không thể đồng bộ ngưỡng với HealthBarUI: " + e.Message);
            }
        }

        // Đăng ký sự kiện thay đổi máu
        healthSystem.OnHealthChanged += OnHealthChanged;

        // Tạo overlay UI
        CreateOverlay();
    }

    private void OnDestroy()
    {
        if (healthSystem != null)
        {
            healthSystem.OnHealthChanged -= OnHealthChanged;
        }
    }

    private void Update()
    {
        if (overlayImage == null || currentIntensity <= 0)
            return;

        // Tính toán hiệu ứng nhấp nháy nếu đã kích hoạt
        if (pulseSpeed > 0 && currentIntensity > 0)
        {
            float pulse = Mathf.Sin(Time.time * pulseSpeed) * pulseIntensity;
            Color color = overlayImage.color;
            color.a = baseAlpha + pulse;
            overlayImage.color = color;
        }
    }

    private void CreateOverlay()
    {
        // Kiểm tra xem đã có canvas chưa
        Canvas existingCanvas = FindObjectOfType<Canvas>();
        GameObject canvasObject;
        
        if (existingCanvas == null)
        {
            // Tạo Canvas mới
            canvasObject = new GameObject("EffectsCanvas");
            Canvas canvas = canvasObject.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvas.sortingOrder = 100; // Đảm bảo hiển thị phía trên
            
            // Thêm Canvas Scaler
            CanvasScaler scaler = canvasObject.AddComponent<CanvasScaler>();
            scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
            scaler.referenceResolution = new Vector2(1920, 1080);
            
            // Thêm Graphic Raycaster
            canvasObject.AddComponent<GraphicRaycaster>();
        }
        else
        {
            canvasObject = existingCanvas.gameObject;
        }

        // Tạo overlay image
        GameObject overlayObject = new GameObject("LowHealthOverlay");
        overlayObject.transform.SetParent(canvasObject.transform, false);
        
        // Thêm và thiết lập image
        overlayImage = overlayObject.AddComponent<Image>();
        overlayImage.color = new Color(0.7f, 0, 0, 0); // Đỏ với alpha = 0
        
        // Thiết lập rect transform để phủ toàn màn hình
        RectTransform rectTransform = overlayObject.GetComponent<RectTransform>();
        rectTransform.anchorMin = Vector2.zero;
        rectTransform.anchorMax = Vector2.one;
        rectTransform.sizeDelta = Vector2.zero;
        rectTransform.anchoredPosition = Vector2.zero;

        // Thêm blur effect (optional)
        // Blur không thể tạo bằng UI thông thường, bạn cần Post Processing
        // Đoạn code này chỉ để tham khảo, bạn cần import Post Processing package
        TryAddPostProcessingBlur();
    }

    private void OnHealthChanged(int currentHealth, int maxHealth)
    {
        if (overlayImage == null)
            return;
            
        // Tính phần trăm máu hiện tại
        float healthPercent = (float)currentHealth / maxHealth;
        
        // Nếu máu dưới ngưỡng, hiển thị hiệu ứng
        if (healthPercent < healthThreshold)
        {
            // Tính cường độ hiệu ứng (càng ít máu, hiệu ứng càng mạnh)
            float healthRatio = healthPercent / healthThreshold;
            currentIntensity = Mathf.Lerp(maxEffectIntensity, 0, healthRatio);
            baseAlpha = currentIntensity;
            
            // Cập nhật màu
            Color color = overlayImage.color;
            color.a = baseAlpha;
            overlayImage.color = color;
        }
        else
        {
            // Tắt hiệu ứng
            currentIntensity = 0;
            Color color = overlayImage.color;
            color.a = 0;
            overlayImage.color = color;
        }
    }
    
    private void TryAddPostProcessingBlur()
    {
        // Hiện tại chỉ sử dụng overlay màu đơn giản
        // Nếu muốn thêm blur effect, bạn cần import Post Processing Stack từ Package Manager
        // Sau đó thêm Vignette và Bloom effects
        Debug.Log("Đang sử dụng overlay màu đơn giản. Để có hiệu ứng blur, cần thêm Post Processing Stack.");
    }
}
