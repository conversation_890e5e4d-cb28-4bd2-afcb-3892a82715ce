using UnityEngine;

// This is a helper script with setup instructions, not meant to be attached to any object
// It documents how to set up the health bar UI in your scene
public class HealthBarSetup : MonoBehaviour
{
    /*
    HOW TO SET UP THE HEALTH BAR UI:
    
    1. Create a Canvas:
       - Right click in Hierarchy -> UI -> Canvas
       - Make sure the Canvas has a Canvas Scaler component
       - Set Render Mode to "Screen Space - Overlay"
       - UI Scale Mode: "Scale With Screen Size"
       - Reference Resolution: 1920x1080 (or your target resolution)
    
    2. Create a Panel:
       - Right click on Canvas -> UI -> Panel
       - Rename it to "HealthBarPanel"
       - Set its color to a semi-transparent dark color (e.g., RGBA: 0,0,0,150)
       - Position it in the desired corner:
         * Anchor Presets: Select your desired corner (e.g., top-left)
         * Pivot: Match with anchor
         * Width: 30-50 pixels
         * Height: 200-300 pixels
         * Position: Adjust margins to place it slightly away from the edge
    
    3. Create the Background Bar:
       - Right click on HealthBarPanel -> UI -> Image
       - Rename it to "HealthBarBackground"
       - Set color to dark gray (e.g., RGBA: 50,50,50,255)
       - Anchor to stretch to fill the panel (minus some padding)
    
    4. Create the Fill Bar:
       - Right click on HealthBarBackground -> UI -> Image
       - Rename it to "HealthBarFill"
       - Set Image Type to "Filled" in the Inspector
       - Fill Method: Set to "Vertical" to fill from bottom to top
       - Fill Origin: Select "Bottom" (0) from the dropdown
       - Set color to green (RGB: 0,255,0) for full health visualization
       - Set Rect Transform to match parent:
         * Right-click on Rect Transform → Set Anchors to stretch all sides
         * Set all position values (left, right, top, bottom) to 0
       - Make sure Fill Amount is set to 1.0 initially (represents 100% health)
       - This image component will be referenced by the HealthBarUI script to display health changes
    
    5. Attach HealthBarUI Script:
       - Add the HealthBarUI script to the HealthBarPanel GameObject
       - Assign the HealthBarFill Image to the "Health Bar Fill" field in the Inspector
       - Adjust the color settings as desired
    
    You're done! The health bar will now display in your UI and respond to health changes.
    You can test it by pressing H (heal) and J (take damage) as set up in the PlayerController.
    */
}
